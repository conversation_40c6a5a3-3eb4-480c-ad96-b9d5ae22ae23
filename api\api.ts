import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';

export const api = axios.create({
  baseURL: 'http://***********:3000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// Request interceptor to add token to all requests except login
api.interceptors.request.use(
  async (config) => {
    // Skip token for auth endpoints
    if (config.url?.includes('/auth/send-otp') || config.url?.includes('/auth/verify-otp')) {
      return config;
    }

    const token = await AsyncStorage.getItem('accessToken');
    console.log('Token:', token);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
     console.log('Token:', config);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
