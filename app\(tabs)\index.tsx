import { Image } from 'expo-image';
import { StyleSheet } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function HomeScreen() {
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <Image
          source={require('@/assets/images/partial-react-logo.png')}
          style={styles.reactLogo}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">Welcome to MeetUp!</ThemedText>
        <HelloWave />
      </ThemedView>
      
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Connect with Friends Nearby</ThemedText>
        <ThemedText>
          MeetUp helps you organize and join local gatherings with friends and community members.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">📅 Create Meetups</ThemedText>
        <ThemedText>
          Tap the <ThemedText type="defaultSemiBold">Create</ThemedText> tab to organize new meetups. 
          Set location, date, time, and invite contacts from your phone.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">👥 Browse & RSVP</ThemedText>
        <ThemedText>
          Check the <ThemedText type="defaultSemiBold">Meetups</ThemedText> tab to see upcoming events. 
          RSVP with Accept, Maybe, or Decline to let organizers know your plans.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">🗺️ Find Friends</ThemedText>
        <ThemedText>
          Use the <ThemedText type="defaultSemiBold">Map</ThemedText> tab to see where your friends 
          are located in real-time and discover meetups happening nearby.
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Get Started</ThemedText>
        <ThemedText>
          Ready to connect? Start by creating your first meetup or browse existing ones to join the community!
        </ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
});

