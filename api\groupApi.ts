import { Group } from "@/types/meetup";
import { api } from "./api";

export const createGroup = async (groupData: Omit<Group, "id">) => {
  try {
    const response = await api.post("/groups", groupData);
    return response.data;
  } catch (error) {
    console.error("Create Group Error:", error);
    throw error;
  }
};

export const getAllGroups = async () => {
  try {
    const response = await api.get("/groups");
    return response.data.results;
  } catch (error) {
    console.error("Get Groups Error:", error);
    throw error;
  }
};

export const addMember = async (groupId: string, userId: string) => {
  try {
    const response = await api.post(`/groups/${groupId}/members`, { userId });
    return response.data;
  } catch (error) {
    console.error("Add Member Error:", error);
    throw error;
  }
};

export const getGroupMembers = async (groupId: string) => {
  try {
    const response = await api.get(`/groups/${groupId}/members`);
    return response.data;
  } catch (error) {
    console.error("Get Group Members Error:", error);
    throw error;
  }
};

