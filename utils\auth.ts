import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';

export const checkTokenExpiry = async (): Promise<boolean> => {
  try {
    const accessTokenExpiry = await AsyncStorage.getItem('accessTokenExpiry');
    const refreshTokenExpiry = await AsyncStorage.getItem('refreshTokenExpiry');
    
    if (!accessTokenExpiry || !refreshTokenExpiry) {
      await clearAuthData();
      return false;
    }
    
    const now = new Date().getTime();
    const accessExpiry = new Date(accessTokenExpiry).getTime();
    const refreshExpiry = new Date(refreshTokenExpiry).getTime();
    
    // If refresh token is expired, logout user
    if (now <= refreshExpiry) {
      await clearAuthData();
      router.replace('/login');
      return false;
    }
    
    // If access token is expired but refresh token is valid
    if (now >= accessExpiry) {
      // TODO: Implement token refresh logic here
      console.log('Access token expired, should refresh');
    }
    
    return true;
  } catch (error) {
    console.error('Error checking token expiry:', error);
    await clearAuthData();
    return false;
  }
};

const clearAuthData = async () => {
  await AsyncStorage.multiRemove([
    'isLoggedIn',
    'userPhone',
    'userData',
    'accessToken',
    'refreshToken',
    'accessTokenExpiry',
    'refreshTokenExpiry'
  ]);
};