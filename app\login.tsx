import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import React, { useRef, useState } from "react";
import {
  Alert,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

import { sendOtp, verifyOtp } from "@/api/userApi";

export default function LoginScreen() {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const otpRefs = useRef<TextInput[]>([]);

  const sendOtpToServer = async () => {
    if (phoneNumber.length !== 10) {
      Alert.alert("Error", "Please enter a valid 10-digit phone number");
      return;
    }

    setIsLoading(true);
    try {
      await sendOtp(phoneNumber);
    } catch (error) {
      Alert.alert("Error", "Failed to send OTP");
      setIsLoading(false);
      return;
    }
    // setTimeout(() => {
    //   setIsOtpSent(true);
    //   setIsLoading(false);
    //   Alert.alert('OTP Sent', 'OTP has been sent to your phone number');
    // }, 1000);
    setIsOtpSent(true);
    setIsLoading(false);
    Alert.alert("OTP Sent", "OTP has been sent to your phone number");
  };

  const verifyOtpFromServer = async () => {
    const otpString = otp.join("");
    if (otpString.length !== 6) {
      Alert.alert("Error", "Please enter complete OTP");
      return;
    }

    setIsLoading(true);
    try {
      console.log("verifyOtpFromServer: ", phoneNumber, otpString);
      const result = await verifyOtp(phoneNumber, otpString);
      console.log("verifyOtpFromServer: ", result);
      
      // Save user data and tokens to AsyncStorage
      await AsyncStorage.setItem("isLoggedIn", "true");
      await AsyncStorage.setItem("userPhone", phoneNumber);
      await AsyncStorage.setItem("userData", JSON.stringify(result.user));
      await AsyncStorage.setItem("accessToken", result.tokens.access.token);
      await AsyncStorage.setItem("refreshToken", result.tokens.refresh.token);
      await AsyncStorage.setItem("accessTokenExpiry", result.tokens.access.expires);
      await AsyncStorage.setItem("refreshTokenExpiry", result.tokens.refresh.expires);
      
    } catch (error) {
      Alert.alert("Error", "Failed to verify OTP");
      setIsLoading(false);
      return;
    }
    setIsLoading(false);
    router.replace("/(tabs)");
  };

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleBackspace = (value: string, index: number) => {
    if (!value && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>MeetUp</Text>
        <Text style={styles.subtitle}>Connect with friends nearby</Text>
      </View>

      {!isOtpSent ? (
        <View style={styles.phoneContainer}>
          <Text style={styles.label}>Enter your phone number</Text>
          <View style={styles.inputContainer}>
            <Text style={styles.countryCode}>+91</Text>
            <TextInput
              style={styles.phoneInput}
              placeholder="Enter 10-digit number"
              placeholderTextColor="#90A4AE"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              keyboardType="numeric"
              maxLength={10}
            />
          </View>
          <TouchableOpacity
            style={[styles.button, isLoading && styles.buttonDisabled]}
            onPress={sendOtpToServer}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>
              {isLoading ? "Sending..." : "Send OTP"}
            </Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.otpContainer}>
          <Text style={styles.label}>Enter OTP sent to +91 {phoneNumber}</Text>
          <View style={styles.otpInputContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => {
                  otpRefs.current[index] = ref!;
                }}
                style={styles.otpInput}
                value={digit}
                onChangeText={(value) => handleOtpChange(value, index)}
                onKeyPress={({ nativeEvent }) => {
                  if (nativeEvent.key === "Backspace") {
                    handleBackspace(digit, index);
                  }
                }}
                keyboardType="numeric"
                maxLength={1}
                textAlign="center"
              />
            ))}
          </View>
          <TouchableOpacity
            style={[styles.button, isLoading && styles.buttonDisabled]}
            onPress={verifyOtpFromServer}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>
              {isLoading ? "Verifying..." : "Verify OTP"}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setIsOtpSent(false)}>
            <Text style={styles.backText}>Change Phone Number</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#E3F2FD",
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginTop: 80,
    marginBottom: 60,
  },
  title: {
    fontSize: 36,
    fontWeight: "bold",
    color: "#1976D2",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#546E7A",
  },
  phoneContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    shadowColor: "#1976D2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  otpContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    shadowColor: "#1976D2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  label: {
    fontSize: 16,
    marginBottom: 20,
    color: "#37474F",
    textAlign: "center",
    fontWeight: "500",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#E1E8ED",
    borderRadius: 12,
    marginBottom: 24,
    backgroundColor: "#F8FAFE",
  },
  countryCode: {
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#1976D2",
    fontWeight: "600",
  },
  phoneInput: {
    flex: 1,
    height: 56,
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#1A1A1A",
  },
  otpInputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  otpInput: {
    width: 48,
    height: 56,
    borderWidth: 2,
    borderColor: "#E1E8ED",
    borderRadius: 12,
    fontSize: 20,
    fontWeight: "bold",
    backgroundColor: "#F8FAFE",
    color: "#1976D2",
  },
  button: {
    backgroundColor: "#1976D2",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#1976D2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonDisabled: {
    backgroundColor: "#90A4AE",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  backText: {
    color: "#1976D2",
    marginTop: 20,
    textAlign: "center",
    fontSize: 16,
    fontWeight: "500",
  },
});

