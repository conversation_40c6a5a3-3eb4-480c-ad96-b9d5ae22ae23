/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#1E88E5';
const tintColorDark = '#64B5F6';

export const Colors = {
  light: {
    text: '#1A1A1A',
    background: '#FFFFFF',
    tint: tintColorLight,
    icon: '#546E7A',
    tabIconDefault: '#90A4AE',
    tabIconSelected: tintColorLight,
    primary: '#1976D2',
    secondary: '#E3F2FD',
    accent: '#2196F3',
    surface: '#F5F5F5',
    border: '#E1E8ED',
  },
  dark: {
    text: '#FFFFFF',
    background: '#0D1117',
    tint: tintColorDark,
    icon: '#8B949E',
    tabIconDefault: '#6E7681',
    tabIconSelected: tintColorDark,
    primary: '#1976D2',
    secondary: '#1A237E',
    accent: '#64B5F6',
    surface: '#161B22',
    border: '#30363D',
  },
};

