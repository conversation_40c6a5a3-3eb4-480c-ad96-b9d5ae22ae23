import { getGroups } from '@/api/groupApi';
import { createMeetup } from '@/api/meetupApi';
import { Contact } from '@/types/contact';
import { Group, Meetup } from '@/types/meetup';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as Contacts from 'expo-contacts';
import * as Location from 'expo-location';
import { useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

export default function CreateMeetupScreen() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [locationAddress, setLocationAddress] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [dateTime, setDateTime] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [showContactsModal, setShowContactsModal] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Group related states
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  
  // Create group states
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newGroupAvatar, setNewGroupAvatar] = useState('');

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      const groupsData = await getGroups();
      setGroups(groupsData);
    } catch (error) {
      console.error('Failed to load groups:', error);
    }
  };

  const onCreateGroup = async () => {
    if (!newGroupName.trim() || !newGroupDescription.trim()) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    try {
      const { createGroup } = await import('@/api/groupApi');
      const groupData: Omit<Group, 'id'> = {
        name: newGroupName,
        description: newGroupDescription,
        avatar: newGroupAvatar || 'https://example.com/default-avatar.jpg',
      };

      const newGroup = await createGroup(groupData);
      setGroups(prev => [...prev, newGroup]);
      setSelectedGroup(newGroup);
      setShowCreateGroupModal(false);
      resetGroupForm();
      Alert.alert('Success', 'Group created successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to create group');
    }
  };

  const resetGroupForm = () => {
    setNewGroupName('');
    setNewGroupDescription('');
    setNewGroupAvatar('');
  };

  const requestContactsPermission = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === 'granted') {
      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers],
      });
      setContacts(data
        .filter(contact => contact.id) // Filter out contacts without IDs
        .map(contact => ({ 
          id: contact.id!, 
          name: contact.name, 
          phoneNumbers: contact.phoneNumbers?.map(phone => ({ 
            number: phone.number || '' 
          })),
          selected: false 
        }))
      );
      setShowContactsModal(true);
    } else {
      Alert.alert('Permission denied', 'Contacts permission is required to invite participants');
    }
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required');
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = currentLocation.coords;
      
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      
      if (reverseGeocode.length > 0) {
        const address = `${reverseGeocode[0].street}, ${reverseGeocode[0].city}`;
        setLocationAddress(address);
        setSelectedLocation({ latitude, longitude });
      }
    } catch (error) {
      Alert.alert('Error', 'Could not get current location');
    }
  };

  const searchLocation = async () => {
    if (!locationAddress.trim()) return;
    
    try {
      const geocode = await Location.geocodeAsync(locationAddress);
      if (geocode.length > 0) {
        setSelectedLocation({
          latitude: geocode[0].latitude,
          longitude: geocode[0].longitude,
        });
        Alert.alert('Success', 'Location found and set!');
      } else {
        Alert.alert('Error', 'Location not found');
      }
    } catch (error) {
      Alert.alert('Error', 'Could not search location');
    }
  };

  const toggleContactSelection = (contactId: string) => {
    setContacts(prev => 
      prev.map(contact => 
        contact.id === contactId 
          ? { ...contact, selected: !contact.selected }
          : contact
      )
    );
  };

  const confirmContactSelection = () => {
    const selected = contacts.filter(contact => contact.selected);
    setSelectedContacts(selected);
    setShowContactsModal(false);
  };

  const onCreateMeetup = async () => {
    if (!title.trim() || !description.trim() || !locationAddress.trim() || !selectedLocation || !selectedGroup) {
      Alert.alert('Error', 'Please fill all required fields including group selection');
      return;
    }

    setLoading(true);
    try {
      const userPhone = await AsyncStorage.getItem('userPhone') || '';
      const participantPhones = selectedContacts
        .flatMap(contact => contact.phoneNumbers?.map(phone => phone.number) || [])
        .filter(phone => phone);

      const meetupData: Omit<Meetup, 'id' | 'rsvp'> = {
        title,
        description,
        location: {
          address: locationAddress,
          latitude: selectedLocation.latitude,
          longitude: selectedLocation.longitude,
        },
        dateTime: dateTime.getTime(),
        participants: [userPhone, ...participantPhones],
        groupId: selectedGroup.id,
      };

      await createMeetup(meetupData);

      Alert.alert('Success', 'Meetup created successfully!', [
        { text: 'OK', onPress: resetForm }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create meetup');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setLocationAddress('');
    setSelectedLocation(null);
    setDateTime(new Date());
    setSelectedContacts([]);
    setSelectedGroup(null);
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const newDateTime = new Date(dateTime);
      newDateTime.setFullYear(selectedDate.getFullYear());
      newDateTime.setMonth(selectedDate.getMonth());
      newDateTime.setDate(selectedDate.getDate());
      setDateTime(newDateTime);
    }
  };

  const onTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const newDateTime = new Date(dateTime);
      newDateTime.setHours(selectedTime.getHours());
      newDateTime.setMinutes(selectedTime.getMinutes());
      setDateTime(newDateTime);
    }
  };

  const renderContact = ({ item }: { item: Contact }) => (
    <TouchableOpacity
      style={[styles.contactItem, item.selected && styles.selectedContact]}
      onPress={() => toggleContactSelection(item.id)}
    >
      <Text style={styles.contactName}>{item.name}</Text>
      <Text style={styles.contactPhone}>
        {item.phoneNumbers?.[0]?.number || 'No phone'}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Create Meetup</Text>
      
      {/* Title Input */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Title *</Text>
        <TextInput
          style={styles.input}
          value={title}
          onChangeText={setTitle}
          placeholder="Enter meetup title"
        />
      </View>

      {/* Description Input */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Description *</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={description}
          onChangeText={setDescription}
          placeholder="Enter meetup description"
          multiline
          numberOfLines={4}
        />
      </View>

      {/* Location Input */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Location *</Text>
        <TextInput
          style={styles.input}
          value={locationAddress}
          onChangeText={setLocationAddress}
          placeholder="Enter location address"
        />
        <View style={styles.locationButtons}>
          <TouchableOpacity style={styles.locationButton} onPress={getCurrentLocation}>
            <Text style={styles.buttonText}>Use Current Location</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.locationButton} onPress={searchLocation}>
            <Text style={styles.buttonText}>Search Location</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Date & Time Picker */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Date & Time</Text>
        <View style={styles.dateTimeContainer}>
          <TouchableOpacity
            style={styles.dateTimeButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateTimeText}>
              {dateTime.toLocaleDateString()}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.dateTimeButton}
            onPress={() => setShowTimePicker(true)}
          >
            <Text style={styles.dateTimeText}>
              {dateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Participants */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Participants</Text>
        <TouchableOpacity
          style={styles.inviteButton}
          onPress={requestContactsPermission}
        >
          <Text style={styles.buttonText}>Invite from Contacts</Text>
        </TouchableOpacity>
        {selectedContacts.length > 0 && (
          <View style={styles.selectedContactsContainer}>
            <Text style={styles.selectedContactsTitle}>
              Selected ({selectedContacts.length}):
            </Text>
            {selectedContacts.map(contact => (
              <Text key={contact.id} style={styles.selectedContactName}>
                • {contact.name}
              </Text>
            ))}
          </View>
        )}
      </View>

      {/* Group Selection */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Group *</Text>
        <TouchableOpacity
          style={styles.groupSelector}
          onPress={() => setShowGroupModal(true)}
        >
          <Text style={[styles.groupSelectorText, !selectedGroup && styles.placeholderText]}>
            {selectedGroup ? selectedGroup.name : 'Select a group'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Create Button */}
      <TouchableOpacity
        style={[styles.createButton, loading && styles.disabledButton]}
        onPress={onCreateMeetup}
        disabled={loading}
      >
        <Text style={styles.createButtonText}>
          {loading ? 'Creating...' : 'Create Meetup'}
        </Text>
      </TouchableOpacity>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={dateTime}
          mode="date"
          display="default"
          onChange={onDateChange}
          minimumDate={new Date()}
        />
      )}

      {/* Time Picker Modal */}
      {showTimePicker && (
        <DateTimePicker
          value={dateTime}
          mode="time"
          display="default"
          onChange={onTimeChange}
        />
      )}

      {/* Contacts Modal */}
      <Modal
        visible={showContactsModal}
        animationType="slide"
        onRequestClose={() => setShowContactsModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Contacts</Text>
            <TouchableOpacity onPress={() => setShowContactsModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={contacts}
            renderItem={renderContact}
            keyExtractor={item => item.id}
            style={styles.contactsList}
          />
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={confirmContactSelection}
          >
            <Text style={styles.confirmButtonText}>
              Confirm Selection ({contacts.filter(c => c.selected).length})
            </Text>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Group Selection Modal */}
      <Modal
        visible={showGroupModal}
        animationType="slide"
        onRequestClose={() => setShowGroupModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Group</Text>
            <TouchableOpacity onPress={() => setShowGroupModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity
            style={styles.createNewGroupButton}
            onPress={() => {
              setShowGroupModal(false);
              setShowCreateGroupModal(true);
            }}
          >
            <Text style={styles.createNewGroupText}>+ Create New Group</Text>
          </TouchableOpacity>

          <FlatList
            data={groups}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.groupItem, selectedGroup?.id === item.id && styles.selectedGroupItem]}
                onPress={() => {
                  setSelectedGroup(item);
                  setShowGroupModal(false);
                }}
              >
                <Text style={styles.groupName}>{item.name}</Text>
                <Text style={styles.groupDescription}>{item.description}</Text>
              </TouchableOpacity>
            )}
            keyExtractor={item => item.id}
            style={styles.groupsList}
          />
        </View>
      </Modal>

      {/* Create Group Modal */}
      <Modal
        visible={showCreateGroupModal}
        animationType="slide"
        onRequestClose={() => setShowCreateGroupModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Create New Group</Text>
            <TouchableOpacity onPress={() => setShowCreateGroupModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.createGroupForm}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Name *</Text>
              <TextInput
                style={styles.input}
                value={newGroupName}
                onChangeText={setNewGroupName}
                placeholder="Enter group name"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Description *</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={newGroupDescription}
                onChangeText={setNewGroupDescription}
                placeholder="Enter group description"
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Avatar URL</Text>
              <TextInput
                style={styles.input}
                value={newGroupAvatar}
                onChangeText={setNewGroupAvatar}
                placeholder="https://example.com/avatar.jpg"
              />
            </View>

            <TouchableOpacity
              style={styles.createButton}
              onPress={onCreateGroup}
            >
              <Text style={styles.createButtonText}>Create Group</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  locationButtons: {
    flexDirection: 'row',
    marginTop: 10,
    gap: 10,
  },
  locationButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  dateTimeButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f9f9f9',
    alignItems: 'center',
  },
  dateTimeText: {
    fontSize: 16,
    color: '#333',
  },
  inviteButton: {
    backgroundColor: '#34C759',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  selectedContactsContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  selectedContactsTitle: {
    fontWeight: '600',
    marginBottom: 5,
  },
  selectedContactName: {
    fontSize: 14,
    color: '#666',
    marginLeft: 10,
  },
  createButton: {
    backgroundColor: '#FF6B6B',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  disabledButton: {
    opacity: 0.6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    color: '#007AFF',
    fontSize: 16,
  },
  contactsList: {
    flex: 1,
  },
  contactItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedContact: {
    backgroundColor: '#e3f2fd',
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
  },
  contactPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  confirmButton: {
    backgroundColor: '#34C759',
    padding: 16,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  groupSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f9f9f9',
  },
  groupSelectorText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  groupItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    color: '#666',
  },
  groupsList: {
    flex: 1,
  },
  createNewGroupButton: {
    backgroundColor: '#34C759',
    padding: 12,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  createNewGroupText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  selectedGroupItem: {
    backgroundColor: '#e3f2fd',
  },
  createGroupForm: {
    flex: 1,
    padding: 20,
  },
});








