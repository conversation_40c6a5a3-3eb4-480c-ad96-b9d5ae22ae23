import AsyncStorage from '@react-native-async-storage/async-storage';
import { api } from './api';

export const getUser = async () => {
  const userPhone = await AsyncStorage.getItem('userPhone');
  if (!userPhone) return null;
  const response = await api.get(`/auth/${userPhone}`);
  return response.data;
};

export const sendOtp = async (phoneNumber: string) => {
  try {
    console.log('Sending OTP to phone number:', phoneNumber);
    const response = await api.post('/auth/send-otp', { phoneNumber });
    console.log('Send OTP Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Send OTP Error:', error);
    throw error;
  }
};

export const verifyOtp = async (phoneNumber: string, otp: string) => {
  try {
    const response = await api.post('/auth/verify-otp', { phoneNumber, otp });
    return response.data;
  } catch (error) {
    console.error('Verify OTP Error:', error);
    throw error;
  }
};
