import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
// // Optionally import the services that you want to use
// // import {...} from 'firebase/auth';
// // import {...} from 'firebase/database';
// // import {...} from 'firebase/firestore';
// // import {...} from 'firebase/functions';
// // import {...} from 'firebase/storage';

const firebaseConfig = {
  apiKey: "AIzaSyBBgmsS9edI0H-eJIxt6GctWGqrobu8HDc",
  authDomain: "meetup-38403.firebaseapp.com",
  databaseURL: "https://meetup-38403-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "meetup-38403",
  storageBucket: "meetup-38403.firebasestorage.app",
  messagingSenderId: "363127606984",
  appId: "1:363127606984:android:07a9c8af01dafb8223dffd"
};

const app = initializeApp(firebaseConfig);
export const database = getDatabase(app);
