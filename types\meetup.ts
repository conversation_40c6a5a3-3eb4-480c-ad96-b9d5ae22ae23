export interface Meetup {
  id: string;
  title: string;
  description: string;
  location: {
    address: string;
    latitude: number;
    longitude: number;
  };
  dateTime: number;
  participants: string[];
  rsvp: { [key: string]: "Accept" | "Decline" | "Maybe" | "Pending" };
  groupId: string;
}

export interface Group {
  id: string;
  name: string;
  description: string;
  avatar: string;
  settings?: {
    isPrivate: boolean;
    allowMemberInvites: boolean;
    requireApproval: boolean;
  };
  isActive?: boolean;
  createdBy?: string;
  members?: GroupMember[];
}

export interface GroupMember {
  _id: string;
  userId: string;
  role: string;
  joinedAt: string;
  user?: {
    name?: string;
    phoneNumber?: string;
  };
}
