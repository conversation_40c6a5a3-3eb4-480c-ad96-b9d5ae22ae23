import { addMember, createGroup, getGroupMembers, getGroups } from '@/api/groupApi';
import { Contact } from '@/types/contact';
import { Group } from '@/types/meetup';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Contacts from 'expo-contacts';
import { useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  Image,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function GroupsScreen() {
  const insets = useSafeAreaInsets();
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showGroupDetailsModal, setShowGroupDetailsModal] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [groupMembers, setGroupMembers] = useState<any[]>([]);
  
  // Contacts for adding members
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [showContactsModal, setShowContactsModal] = useState(false);
  
  // Create group states
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [avatar, setAvatar] = useState('');
  const [createLoading, setCreateLoading] = useState(false);

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    setLoading(true);
    try {
      const groupsData = await getGroups();
      setGroups(groupsData);
    } catch (error) {
      console.error('Failed to load groups:', error);
      Alert.alert('Error', 'Failed to load groups');
    } finally {
      setLoading(false);
    }
  };

  const onCreateGroup = async () => {
    if (!name.trim() || !description.trim()) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    setCreateLoading(true);
    try {
      const groupData: Omit<Group, 'id'> = {
        name,
        description,
        avatar: avatar || 'https://example.com/default-avatar.jpg',
      };

      const newGroup = await createGroup(groupData);
      setGroups(prev => [newGroup, ...prev]);
      setShowCreateModal(false);
      resetForm();
      
      // Open group details to add members
      setSelectedGroup(newGroup);
      setShowGroupDetailsModal(true);
      
      Alert.alert('Success', 'Group created successfully! You can now add members.');
    } catch (error) {
      Alert.alert('Error', 'Failed to create group');
    } finally {
      setCreateLoading(false);
    }
  };

  const resetForm = () => {
    setName('');
    setDescription('');
    setAvatar('');
  };

  const openGroupDetails = async (group: Group) => {
    setSelectedGroup(group);
    try {
      const members = await getGroupMembers(group.id);
      setGroupMembers(members);
    } catch (error) {
      console.error('Failed to load group members:', error);
      setGroupMembers([]);
    }
    setShowGroupDetailsModal(true);
  };

  const requestContactsPermission = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === 'granted') {
      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers],
      });
      setContacts(data
        .filter(contact => contact.id) // Filter out contacts without IDs
        .map(contact => ({ 
          id: contact.id!, 
          name: contact.name, 
          phoneNumbers: contact.phoneNumbers?.map(phone => ({ 
            number: phone.number || '' 
          })),
          selected: false 
        }))
      );
      setShowContactsModal(true);
    } else {
      Alert.alert('Permission denied', 'Contacts permission is required to add members');
    }
  };

  const toggleContactSelection = (contactId: string) => {
    setContacts(prev => 
      prev.map(contact => 
        contact.id === contactId 
          ? { ...contact, selected: !contact.selected }
          : contact
      )
    );
  };

  const addSelectedMembers = async () => {
    if (!selectedGroup) return;
    
    const selectedContacts = contacts.filter(contact => contact.selected);
    if (selectedContacts.length === 0) {
      Alert.alert('No contacts selected', 'Please select at least one contact to add');
      return;
    }

    try {
      const userPhone = await AsyncStorage.getItem('userPhone') || '';
      
      // Get all phone numbers from selected contacts
      const phoneNumbers = selectedContacts.flatMap(
        contact => contact.phoneNumbers?.map(phone => phone.number) || []
      );
      
      // Add each member to the group
      for (const phoneNumber of phoneNumbers) {
        if (phoneNumber) {
          await addMember(selectedGroup.id, phoneNumber);
        }
      }
      
      // Refresh members list
      const members = await getGroupMembers(selectedGroup.id);
      setGroupMembers(members);
      
      setShowContactsModal(false);
      Alert.alert('Success', 'Members added to the group');
    } catch (error) {
      console.error('Failed to add members:', error);
      Alert.alert('Error', 'Failed to add members to the group');
    }
  };

  const renderGroup = ({ item }: { item: Group }) => (
    <TouchableOpacity 
      style={styles.groupCard}
      onPress={() => openGroupDetails(item)}
    >
      <Image 
        source={{ uri: item.avatar }} 
        style={styles.groupAvatar}
        defaultSource={require('@/assets/images/default-group.png')}
      />
      <View style={styles.groupInfo}>
        <Text style={styles.groupName}>{item.name}</Text>
        <Text style={styles.groupDescription}>{item.description}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderContact = ({ item }: { item: Contact }) => (
    <TouchableOpacity
      style={[styles.contactItem, item.selected && styles.selectedContact]}
      onPress={() => toggleContactSelection(item.id)}
    >
      <Text style={styles.contactName}>{item.name}</Text>
      <Text style={styles.contactPhone}>
        {item.phoneNumbers?.[0]?.number || 'No phone'}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
        <Text style={styles.title}>Groups</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Text style={styles.createButtonText}>+ Create</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text>Loading groups...</Text>
        </View>
      ) : (
        <FlatList
          data={groups}
          renderItem={renderGroup}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          refreshing={loading}
          onRefresh={loadGroups}
        />
      )}

      {/* Create Group Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Create New Group</Text>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.createForm}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Name *</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="Enter group name"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Description *</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Enter group description"
                multiline
                numberOfLines={4}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Avatar URL</Text>
              <TextInput
                style={styles.input}
                value={avatar}
                onChangeText={setAvatar}
                placeholder="https://example.com/avatar.jpg"
              />
            </View>

            <TouchableOpacity
              style={[styles.submitButton, createLoading && styles.disabledButton]}
              onPress={onCreateGroup}
              disabled={createLoading}
            >
              <Text style={styles.submitButtonText}>
                {createLoading ? 'Creating...' : 'Create Group'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>

      {/* Group Details Modal */}
      <Modal
        visible={showGroupDetailsModal}
        animationType="slide"
        onRequestClose={() => setShowGroupDetailsModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{selectedGroup?.name}</Text>
            <TouchableOpacity onPress={() => setShowGroupDetailsModal(false)}>
              <Text style={styles.cancelButton}>Close</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.detailsContainer}>
            <View style={styles.groupDetailHeader}>
              <Image 
                source={{ uri: selectedGroup?.avatar }} 
                style={styles.detailAvatar}
                defaultSource={require('@/assets/images/default-group.png')}
              />
              <View style={styles.groupDetailInfo}>
                <Text style={styles.detailName}>{selectedGroup?.name}</Text>
                <Text style={styles.detailDescription}>{selectedGroup?.description}</Text>
              </View>
            </View>
            
            <View style={styles.membersSection}>
              <View style={styles.membersSectionHeader}>
                <Text style={styles.sectionTitle}>Members</Text>
                <TouchableOpacity
                  style={styles.addMemberButton}
                  onPress={requestContactsPermission}
                >
                  <Text style={styles.addMemberText}>+ Add Members</Text>
                </TouchableOpacity>
              </View>
              
              {groupMembers.length > 0 ? (
                <View style={styles.membersList}>
                  {groupMembers.map((member, index) => (
                    <View key={index} style={styles.memberItem}>
                      <Text style={styles.memberName}>
                        {member?.user?.name || member?.user?.phoneNumber || member?.userId || 'Unknown Member'}
                      </Text>
                      {member?.role && (
                        <Text style={styles.memberRole}>{member.role}</Text>
                      )}
                    </View>
                  ))}
                </View>
              ) : (
                <Text style={styles.noMembersText}>No members yet</Text>
              )}
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Contacts Modal */}
      <Modal
        visible={showContactsModal}
        animationType="slide"
        onRequestClose={() => setShowContactsModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Contacts</Text>
            <TouchableOpacity onPress={() => setShowContactsModal(false)}>
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={contacts}
            renderItem={renderContact}
            keyExtractor={item => item.id}
            style={styles.contactsList}
          />
          
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={addSelectedMembers}
          >
            <Text style={styles.confirmButtonText}>
              Add Selected ({contacts.filter(c => c.selected).length})
            </Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight ? StatusBar.currentHeight + 20 : 40,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  createButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  createButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
  },
  groupCard: {
    flexDirection: 'row',
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  groupAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  groupDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    color: '#007AFF',
    fontSize: 16,
  },
  createForm: {
    flex: 1,
    padding: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#FF6B6B',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  detailsContainer: {
    flex: 1,
    padding: 20,
  },
  groupDetailHeader: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  detailAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 20,
  },
  groupDetailInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  detailName: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  detailDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  membersSection: {
    marginTop: 20,
  },
  membersSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  addMemberButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addMemberText: {
    color: 'white',
    fontWeight: '500',
  },
  membersList: {
    marginTop: 10,
  },
  memberItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  memberName: {
    fontSize: 16,
  },
  memberRole: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  noMembersText: {
    fontSize: 16,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  contactItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  selectedContact: {
    backgroundColor: '#e3f2fd',
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    color: '#666',
  },
  contactsList: {
    flex: 1,
  },
  confirmButton: {
    backgroundColor: '#34C759',
    padding: 16,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});





