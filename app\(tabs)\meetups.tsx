import { database } from '@/config/firebase';
import { Meetup } from '@/types/meetup';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { onValue, ref, update } from 'firebase/database';
import { useEffect, useState } from 'react';
import {
  Al<PERSON>,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

export default function MeetupsScreen() {
  const [meetups, setMeetups] = useState<Meetup[]>([]);
  const [userPhone, setUserPhone] = useState<string>('');

  useEffect(() => {
    initializeUser();
    listenToMeetups();
  }, []);

  const initializeUser = async () => {
    const phone = await AsyncStorage.getItem('userPhone') || '';
    setUserPhone(phone);
  };

  const listenToMeetups = () => {
    const meetupsRef = ref(database, 'meetups');
    onValue(meetupsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const meetupsList = Object.entries(data).map(([key, value]: [string, any]) => ({
          id: key,
          ...value
        }));
        setMeetups(meetupsList.sort((a, b) => b.dateTime - a.dateTime));
      }
    });
  };

  const updateRSVP = async (meetupId: string, response: 'accept' | 'decline' | 'maybe') => {
    if (!userPhone) return;
    
    try {
      const rsvpRef = ref(database, `meetups/${meetupId}/rsvp/${userPhone}`);
      await update(ref(database, `meetups/${meetupId}`), {
        [`rsvp/${userPhone}`]: response
      });
      Alert.alert('Success', `RSVP updated to ${response}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to update RSVP');
    }
  };

  const getRSVPCounts = (rsvp: { [key: string]: string }) => {
    const counts = { accept: 0, decline: 0, maybe: 0 };
    Object.values(rsvp).forEach(response => {
      if (response in counts) {
        counts[response as keyof typeof counts]++;
      }
    });
    return counts;
  };

  const renderMeetup = ({ item }: { item: Meetup }) => {
    const userRSVP = item.rsvp[userPhone];
    const rsvpCounts = getRSVPCounts(item.rsvp);
    const isUpcoming = item.dateTime > Date.now();

    return (
      <View style={styles.meetupCard}>
        <Text style={styles.meetupTitle}>{item.title}</Text>
        <Text style={styles.meetupDescription}>{item.description}</Text>
        <Text style={styles.meetupLocation}>📍 {item.location.address}</Text>
        <Text style={styles.meetupDateTime}>
          🕒 {new Date(item.dateTime).toLocaleString()}
        </Text>
        
        <View style={styles.rsvpCounts}>
          <Text style={styles.rsvpCount}>✅ {rsvpCounts.accept}</Text>
          <Text style={styles.rsvpCount}>❌ {rsvpCounts.decline}</Text>
          <Text style={styles.rsvpCount}>❓ {rsvpCounts.maybe}</Text>
        </View>

        {isUpcoming && (
          <View style={styles.rsvpButtons}>
            <TouchableOpacity
              style={[styles.rsvpButton, styles.acceptButton, userRSVP === 'Accept' && styles.selectedButton]}
              onPress={() => updateRSVP(item.id, 'accept')}
            >
              <Text style={styles.rsvpButtonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.rsvpButton, styles.maybeButton, userRSVP === 'Maybe' && styles.selectedButton]}
              onPress={() => updateRSVP(item.id, 'maybe')}
            >
              <Text style={styles.rsvpButtonText}>Maybe</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.rsvpButton, styles.declineButton, userRSVP === 'Decline' && styles.selectedButton]}
              onPress={() => updateRSVP(item.id, 'decline')}
            >
              <Text style={styles.rsvpButtonText}>Decline</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {!isUpcoming && (
          <Text style={styles.pastEvent}>Past Event</Text>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Meetups</Text>
      <FlatList
        data={meetups}
        renderItem={renderMeetup}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    padding: 20,
    textAlign: 'center',
    backgroundColor: 'white',
  },
  listContainer: {
    padding: 15,
  },
  meetupCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  meetupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  meetupDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  meetupLocation: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 4,
  },
  meetupDateTime: {
    fontSize: 14,
    color: '#FF6B6B',
    marginBottom: 12,
  },
  rsvpCounts: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 15,
  },
  rsvpCount: {
    fontSize: 14,
    fontWeight: '600',
  },
  rsvpButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  rsvpButton: {
    flex: 1,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#34C759',
  },
  maybeButton: {
    backgroundColor: '#FF9500',
  },
  declineButton: {
    backgroundColor: '#FF3B30',
  },
  selectedButton: {
    opacity: 0.8,
    borderWidth: 2,
    borderColor: '#333',
  },
  rsvpButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  pastEvent: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    marginTop: 8,
  },
});
