import { database } from '@/config/firebase';
import { User } from '@/types/user';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { onValue, ref, set } from 'firebase/database';
import { useEffect, useRef, useState } from 'react';
import { <PERSON>ert, Button, Image, StyleSheet, Text, View } from 'react-native';
import MapView, { Marker } from 'react-native-maps';

export default function TabTwoScreen() {
  const mapRef = useRef<MapView>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [userPhone, setUserPhone] = useState<string>('');

  useEffect(() => {
    initializeUser();
    listenToUsers();
  }, []);

  const initializeUser = async () => {
    const phone = await AsyncStorage.getItem('userPhone') || '';
    setUserPhone(phone);
    getCurrentLocation();
  };

  const listenToUsers = () => {
    const usersRef = ref(database, 'users');
    onValue(usersRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const usersList = Object.entries(data).map(([key, value]: [string, any]) => ({
          id: key,
          ...value
        }));
        setUsers(usersList);
      }
    });
  };

  const updateUserLocation = async (latitude: number, longitude: number) => {
    if (!userPhone) return;
    
    const userRef = ref(database, `users/${userPhone}`);
    const userData = {
      name: userPhone.slice(-4), // Use last 4 digits as name
      latitude,
      longitude,
      avatar: `https://via.placeholder.com/40x40/FF6B6B/FFFFFF?text=${userPhone.slice(-2)}`,
      lastUpdated: Date.now()
    };
    
    await set(userRef, userData);
    setCurrentUser({ id: userPhone, ...userData });
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required');
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = currentLocation.coords;
      
      await updateUserLocation(latitude, longitude);
      
      // Update location every 30 seconds
      setInterval(async () => {
        const newLocation = await Location.getCurrentPositionAsync({});
        await updateUserLocation(newLocation.coords.latitude, newLocation.coords.longitude);
      }, 30000);
      
    } catch (error) {
      Alert.alert('Error', 'Could not get current location');
    }
  };

  const resetMap = () => {
    if (currentUser) {
      mapRef.current?.animateToRegion({
        latitude: currentUser.latitude,
        longitude: currentUser.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      }, 1000);
    }
  };

  return (
    <View style={styles.container}>
      <MapView 
        ref={mapRef}
        style={styles.map} 
        showsUserLocation={true}
        showsMyLocationButton={true}
        initialRegion={{
          latitude: currentUser?.latitude || 30.116171,
          longitude: currentUser?.longitude || 78.297684,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}>
        {users.map((user) => (
          <Marker 
            key={user.id}
            coordinate={{ latitude: user.latitude, longitude: user.longitude }}
            title={user.name}
            description={`Last updated: ${new Date(user.lastUpdated).toLocaleTimeString()}`}
          >
            <View style={styles.avatarContainer}>
              <Image 
                source={{ uri: user.avatar }} 
                style={styles.avatarImage}
              />
              <Text style={styles.avatarName}>{user.name}</Text>
            </View>
          </Marker>
        ))}
      </MapView>
      <View style={styles.buttonContainer}>
        <Button title="Reset Map" onPress={resetMap} />
      </View>
      <View style={styles.distanceContainer}>
        <Text style={styles.distanceText}>Active Users: {users.length}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
   container: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  buttonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
  },
  distanceContainer: {
    position: 'absolute',
    bottom: 50,
    left: 20,
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 5,
  },
  distanceText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  avatarContainer: {
    alignItems: 'center',
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderColor: 'white',
  },
  avatarName: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: 'white',
    padding: 4,
    borderRadius: 8,
    fontSize: 10,
    fontWeight: 'bold',
    marginTop: 4,
    minWidth: 40,
    textAlign: 'center',
  },
});



















