// import AsyncStorage from '@react-native-async-storage/async-storage';
import { Meetup } from '@/types/meetup';
import { api } from './api';



interface MeetupApi {
  title: string;
  description: string;
  location: {
    name: string;
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    placeId: string;
  };
  dateTime: string;
  duration: number;
  groupId: string;
  isPrivate: false;
  maxParticipants: number;
}


export const createMeetup = async (meetupData: Omit<Meetup, 'id' | 'rsvp'>) => {
  const aMeetupData: MeetupApi = {
    title: meetupData.title,
    description: meetupData.description,
    location: {
      name: meetupData.location.address,
      address: meetupData.location.address,
      coordinates: {
        lat: meetupData.location.latitude,
        lng: meetupData.location.longitude,
      },
      placeId: '',
    },
    dateTime: new Date(meetupData.dateTime).toISOString(),
    duration: 60,
    groupId: meetupData.groupId || '',
    isPrivate: false,
    maxParticipants: 10,
  }
  try { 
    const response = await api.post('/meetups', aMeetupData);
    return response.data;
  } catch (error) {
    console.error('Create Meetup Error:', error);
    throw error;
  }
};

export const getMeetups = async () => { 
  try { 
    const response = await api.get('/meetups');
    return response.data;
  } catch (error) {
    console.error('Get Meetups Error:', error);
    throw error;
  }
};
